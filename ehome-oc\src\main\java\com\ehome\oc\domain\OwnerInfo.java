package com.ehome.oc.domain;

import java.io.Serializable;
import java.util.List;

/**
 * 一个小区一个业主，但是存在多个小区有同一个人（通过手机号码区分）
 * 如果一个人存在多个小区，应该每个小区创建一个业主，这个业主绑定当前小区的房屋
 */
public class OwnerInfo implements Serializable {
    private String ownerId;
    private String ownerName;
    private String communityId;
    private String communityName;
    private String mobile;
    private String role;
    private int houseCount = 0;
    private String houseName;
    private String houseStr;
    private String houseId;

    private int isDefaultOwnner = 1; //是否默认上次选择的业主 1是 0否
    private List<HouseInfo> houseList; //当前业主（某小区）名下的房屋列表

    public String getOwnerId() {
        return ownerId;
    }
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
    public String getCommunityId() {
        return communityId;
    }
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    public String getMobile() {
        return mobile;
    }
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getRole() {
        return role;
    }

    //role  1业主，2业委会
    public void setRole(String role) {
        this.role = role;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getHouseStr() {
        return houseStr;
    }
    public void setHouseStr(String houseStr) {
        this.houseStr = houseStr;
    }

    public int getHouseCount() {
        return houseCount;
    }

    public void setHouseCount(int houseCount) {
        this.houseCount = houseCount;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public List<HouseInfo> getHouseList() {
        return houseList;
    }

    public void setHouseList(List<HouseInfo> houseList) {
        this.houseList = houseList;
    }

}
