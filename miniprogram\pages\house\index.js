import {getStateManager} from '../../utils/stateManager.js'
import {getLoadingManager, handleError} from '../../utils/errorHandler.js'

const app = getApp()
const stateManager = getStateManager()
const loadingManager = getLoadingManager()

Page({
  data: {
    houseList: [],
    groupedHouseList: [], // 按小区分组的房屋列表
    loading: false,
    isLogin: false,
    hasLoaded: false // 添加标记，记录是否已经加载过数据
  },

  onLoad(options) {
    this.initializePage()

    // 处理从首页传来的action参数
    if (options.action === 'invite') {
      // 延迟执行，确保页面加载完成
      setTimeout(() => {
        this.addVisit()
      }, 1000)
    }
  },

  onShow() {
    // 只在首次加载或需要刷新时才重新获取数据
    if (!this.data.hasLoaded) {
      this.refreshPageData()
    } else {
      // 只刷新页面状态，不重新加载数据
      this.refreshPageState()
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      this.setData({ hasLoaded: false }) // 重置加载标记
      await this.loadHouseList()
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      })
    } catch (error) {
      wx.showToast({
        title: '刷新失败',
        icon: 'none'
      })
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  // 初始化页面
  async initializePage() {
    this.refreshPageData()
    await this.loadHouseList()
  },

  // 刷新页面数据
  refreshPageData() {
    this.refreshPageState()
  },

  // 获取房屋列表
  async loadHouseList() {
    if (!this.checkLoginStatus()) return
    
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: '/api/wx/house/list',
        method: 'GET'
      })

      if (res.code === 0) {
        const list = this.processHouseList(res.data.list || [])
        const groupedList = this.groupHousesByCommunity(list)
        this.setData({
          houseList: list,
          groupedHouseList: groupedList,
          hasLoaded: true // 标记数据已加载
        })
      } else {
        throw new Error(res.msg || '获取房屋列表失败')
      }
    } catch (error) {
      // 如果是登录过期错误，不显示错误提示，让全局处理
      if (error.message && error.message.includes('登录')) {
        return
      }
      
      handleError(error, '获取房屋列表')
      this.setData({ houseList: [] })
    } finally {
      loadingManager.hide()
    }
  },

  // 处理房屋列表数据
  processHouseList(list) {
    return list.map(item => {
      // 判断是否为默认房屋：需要同时满足isDefaultOwner=1和isDefault=1
      const isDefaultHouse = (item.ownerInfo?.isDefaultOwner === 1) && (item.isDefault === 1)

      return {
        ...item,
        id: item.houseId || item.id, // 优先使用houseId
        building: item.building_name || item.building || '', // 映射楼栋名称
        unit: item.unit_name || item.unit || '', // 映射单元名称
        statusText: this.getStatusText(item.checkStatus),
        isDefaultText: isDefaultHouse ? '默认房屋' : '',
        isDefaultHouse: isDefaultHouse, // 添加计算后的默认房屋标识
        userTypeText: this.getUserTypeText(item.houseStatus) // 使用houseStatus字段中的关系类型
      }
    })
  },

  // 按小区分组房屋列表
  groupHousesByCommunity(houseList) {
    const groups = new Map()

    // 按小区ID分组
    houseList.forEach(house => {
      const communityId = house.communityId || house.community_id
      const communityName = house.communityName || house.community_name

      if (!groups.has(communityId)) {
        groups.set(communityId, {
          communityId: communityId,
          communityName: communityName,
          houses: [],
          houseCount: 0
        })
      }

      groups.get(communityId).houses.push(house)
      groups.get(communityId).houseCount++
    })

    // 转换为数组并添加显示标题
    const groupedList = Array.from(groups.values()).map(group => ({
      ...group,
      displayTitle: group.houseCount > 1 ?
        `${group.communityName}（${group.houseCount}套）` :
        group.communityName
    }))

    // 按默认房屋排序：有默认房屋的小区排在前面
    groupedList.sort((a, b) => {
      const aHasDefault = a.houses.some(house => house.isDefaultHouse === true)
      const bHasDefault = b.houses.some(house => house.isDefaultHouse === true)

      if (aHasDefault && !bHasDefault) return -1
      if (!aHasDefault && bHasDefault) return 1
      return 0
    })

    return groupedList
  },

  // 更新本地房屋缓存
  async updateLocalHouseCache() {
    try {
      // 使用新的StatusDataManager强制刷新缓存
      const { getStatusDataManager, FETCH_MODES } = require('../../utils/statusDataManager.js')
      const statusManager = getStatusDataManager()

      const state = stateManager.getState()
      const result = await statusManager.getStatusData({
        mode: FETCH_MODES.FORCE_REFRESH,
        params: {
          ownerInfo: state.ownerInfo,
          userInfo: state.userInfo,
          tokenUser: state.tokenUser
        },
        silent: true // 静默模式，不显示错误
      })

      if (result.success) {
        console.log('[UpdateCache] 房屋缓存更新成功')

        // StatusDataManager已经更新了状态管理器，这里不需要重复更新

        // 如果有新token，更新存储
        if (result.token) {
          wx.setStorageSync('token', result.token)
        }
      } else {
        console.warn('[UpdateCache] 更新本地房屋缓存失败:', result.error)
      }
    } catch (error) {
      console.warn('[UpdateCache] 更新本地房屋缓存异常:', error)
      // 不显示错误提示，因为这是后台操作
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待审核',
      1: '已认证',
      2: '已拒绝'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取住户类型文本
  getUserTypeText(relType) {
    // relType现在是字符串格式的关系类型
    const typeMap = {
      '1': '业主',
      '2': '家庭成员',
      '3': '租户'
    }
    return typeMap[relType] || '业主'
  },

  // 邀请住户
  addVisit() {
    if (!this.checkLoginStatus()) return

    // 检查是否有已认证的房屋
    const authenticatedHouses = this.data.houseList.filter(house => house.checkStatus === 1)

    if (authenticatedHouses.length === 0) {
      wx.showToast({
        title: '请先认证房屋',
        icon: 'none'
      })
      return
    }

    // 如果只有一套已认证房屋，直接跳转到新的邀请页面
    if (authenticatedHouses.length === 1) {
      wx.navigateTo({
        url: `/pages/invite/create?houseId=${authenticatedHouses[0].id}`
      })
      return
    }

    // 多套房屋时，显示选择列表
    const itemList = authenticatedHouses.map(house =>
      `${house.communityName} ${house.combinaName}/${house.room}`
    )

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedHouse = authenticatedHouses[res.tapIndex]
        wx.navigateTo({
          url: `/pages/invite/create?houseId=${selectedHouse.id}`
        })
      }
    })
  },

  // 邀请管理
  manageInvites() {
    if (!this.checkLoginStatus()) return

    wx.navigateTo({
      url: '/pages/house/invite-manage'
    })
  },

  // 解除房屋绑定
  unbindHouse() {
    if (!this.checkLoginStatus()) return

    // 检查是否有房屋
    if (this.data.houseList.length === 0) {
      wx.showToast({
        title: '暂无房屋可解绑',
        icon: 'none'
      })
      return
    }

    // 如果只有一套房屋，直接确认解绑
    if (this.data.houseList.length === 1) {
      const house = this.data.houseList[0]
      this.confirmUnbindHouse(house)
      return
    }

    // 多套房屋时，显示选择列表
    const itemList = this.data.houseList.map(house =>
      `${house.communityName} ${house.building}/${house.unit}/${house.room}`
    )

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedHouse = this.data.houseList[res.tapIndex]
        this.confirmUnbindHouse(selectedHouse)
      }
    })
  },

  // 确认解绑房屋
  confirmUnbindHouse(house) {
    const isDefault = house.isDefault === 1
    const warningText = isDefault ?
      '该房屋是您的默认房屋，解绑后将自动设置其他房屋为默认。' :
      '解绑后您将失去该房屋的相关权限。'

    wx.showModal({
      title: '确认解除绑定',
      content: `确定要解除与"${house.communityName} ${house.building}/${house.unit}/${house.room}"的绑定关系吗？\n\n${warningText}`,
      confirmText: '确认解绑',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.performUnbindHouse(house.id)
        }
      }
    })
  },

  // 执行解绑房屋
  async performUnbindHouse(houseId) {
    try {
      loadingManager.show('解绑中...')

      const res = await app.request({
        url: '/api/wx/house/unbind',
        method: 'POST',
        data: { houseId }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '解绑成功',
          icon: 'success'
        })

        // 重置加载标记，强制刷新数据
        this.setData({ hasLoaded: false })

        // 刷新房屋列表
        await this.loadHouseList()

        // 更新本地房屋缓存
        await this.updateLocalHouseCache()
      } else {
        wx.showToast({
          title: res.msg || '解绑失败',
          icon: 'none'
        })
        throw new Error(res.msg || '解绑失败')
      }
    } catch (error) {
      handleError(error, '解除房屋绑定')
    } finally {
      loadingManager.hide()
    }
  },



  // 添加新房屋
  addHouse() {
    if (!this.checkLoginStatus()) return
    
    if (this.data.houseList.length >= 5) {
      wx.showToast({
        title: '最多只能添加5套房屋',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: '/pages/house/add'
    })
  },

  // 查看房屋详情
  editHouse(e) {
    if (!this.checkLoginStatus()) return
    
    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: `/pages/house/edit?id=${id}`
    })
  },

  // 设为默认房屋
  async setDefault(e) {
    if (!this.checkLoginStatus()) return

    const id = e.currentTarget.dataset.id
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }

    try {
      loadingManager.show('设置中...')

      const res = await app.request({
        url: '/api/wx/house/set-default',
        method: 'POST',
        data: { id }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        })

        // 重置加载标记，强制刷新数据
        this.setData({ hasLoaded: false })

        // 刷新列表
        await this.loadHouseList()

        // 更新本地房屋缓存，这会触发状态管理器更新
        await this.updateLocalHouseCache()

        // 通知其他页面刷新（如导航栏位置信息）
        this.notifyGlobalRefresh()
      } else {
        throw new Error(res.msg || '设置失败')
      }
    } catch (error) {
      handleError(error, '设置默认房屋')
    } finally {
      loadingManager.hide()
    }
  },

  // 通知全局刷新
  notifyGlobalRefresh() {
    // 发送全局事件，通知其他组件刷新
    const eventChannel = getApp().globalData.eventChannel
    if (eventChannel) {
      eventChannel.emit('defaultHouseChanged')
    }

    // 也可以通过页面栈通知其他页面
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.onDefaultHouseChanged && typeof page.onDefaultHouseChanged === 'function') {
        page.onDefaultHouseChanged()
      }
    })
  },

  // 删除房屋
  deleteHouse(e) {
    if (!this.checkLoginStatus()) return
    
    const id = e.currentTarget.dataset.id
    const name = e.currentTarget.dataset.name
    
    if (!id) {
      wx.showToast({
        title: '房屋ID无效',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除房屋"${name}"吗？`,
      confirmText: '删除',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteHouse(id)
        }
      }
    })
  },

  // 执行删除房屋
  async performDeleteHouse(id) {
    try {
      loadingManager.show('删除中...')
      
      const res = await app.request({
        url: '/api/wx/house/delete',
        method: 'POST',
        data: { id }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })
        
        // 刷新列表
        await this.loadHouseList()
      } else {
        throw new Error(res.msg || '删除失败')
      }
    } catch (error) {
      handleError(error, '删除房屋')
    } finally {
      loadingManager.hide()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const state = stateManager.getState()
    const isLogin = state.isLogin
    this.setData({ isLogin })

    if (!isLogin) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return false
    }
    return true
  },

  // 刷新页面状态
  refreshPageState() {
    const state = stateManager.getState()
    this.setData({
      isLogin: state.isLogin
    })
  }
})