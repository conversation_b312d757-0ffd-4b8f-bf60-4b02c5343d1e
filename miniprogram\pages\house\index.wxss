/* 房屋页面 */
page {
  height: 100vh;
  background: #f7f8fa;
}

.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 24rpx 24rpx 140rpx;
  box-sizing: border-box;
}

.house-list {
  margin-bottom: 32rpx;
}

/* 小区分组样式 */
.community-group {
  margin-bottom: 32rpx;
}

.community-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx 12rpx 0 0;
  border: 1rpx solid #ebedf0;
  border-bottom: none;
  margin-bottom: 0;
}

.community-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

.community-group .house-card:first-child {
  border-radius: 0;
  margin-bottom: 0;
  border-top: none;
}

.community-group .house-card:last-child {
  border-radius: 0 0 12rpx 12rpx;
  margin-bottom: 0;
}

.community-group .house-card:not(:last-child) {
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 0;
  border-radius: 0;
}

/* 房屋卡片样式 */
.house-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #ebedf0;
  overflow: hidden;
}

/* 标题区域 */
.house-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.house-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

/* 内容区域 */
.house-content {
  padding: 20rpx 24rpx;
}

.content-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.content-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
}

/* 底部状态区域 */
.house-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #f5f5f5;
  background: #fafafa;
}

.default-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.default-status:active {
  opacity: 0.7;
}

.default-status.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.status-text {
  font-size: 28rpx;
  color: #999;
}

.status-text.active {
  color: #52c41a;
  font-weight: 500;
}

.auth-status {
  display: flex;
  align-items: center;
}

/* 底部操作按钮区域 */
.bottom-actions {
  display: flex;
  gap: 24rpx;
  padding: 20rpx 32rpx 32rpx;
  justify-content: center;
  flex-wrap: nowrap;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f7f8fa;
  border-top: 1rpx solid #ebedf0;
  z-index: 100;
}

.bottom-btn {
  flex: 1 !important;
  min-width: 200rpx !important;
  max-width: 240rpx !important;
  border-radius: 8rpx !important;
  height: 88rpx !important;
  font-size: 26rpx !important;
  font-weight: 400 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  line-height: 1.2 !important;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding-bottom: 120rpx;
  }

  .bottom-actions {
    gap: 16rpx;
    padding: 16rpx 24rpx 24rpx;
  }

  .bottom-btn {
    min-width: 180rpx !important;
    max-width: 200rpx !important;
    font-size: 24rpx !important;
    height: 80rpx !important;
  }
}

/* 小屏幕适配 - 当屏幕太小时改为垂直布局 */
@media (max-width: 650rpx) {
  .container {
    padding-bottom: 280rpx;
  }

  .bottom-actions {
    flex-direction: column;
    gap: 20rpx;
    padding: 20rpx 40rpx 32rpx;
  }

  .bottom-btn {
    flex: none !important;
    width: 100% !important;
    min-width: auto !important;
    max-width: none !important;
    height: 88rpx !important;
    font-size: 28rpx !important;
  }
}

.unbind-btn {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

.unbind-btn:active {
  background-color: #d9363e !important;
  border-color: #d9363e !important;
}

.van-button--normal{
  padding: 0 16rpx !important;
}