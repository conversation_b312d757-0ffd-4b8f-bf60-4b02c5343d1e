package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.service.IHouseInfoService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/wx/house")
public class HouseController extends BaseWxController {

    @Autowired
    private IHouseInfoService houseInfoService;

    /**
     * 获取房屋列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        try {
            Long wxUserId = getCurrentUserId();
            if (wxUserId == null) {
                return AjaxResult.error("用户未登录");
            }
            if(StringUtils.isEmpty(getCurrentUser().getMobile())){
                return AjaxResult.error("请先绑定手机号");
            }
            List<HouseInfo> list = new ArrayList<HouseInfo>();
            List<Record> houseRecordList = Db.find(
                "SELECT t1.*, t3.is_default owner_default, t3.is_default user_default, t3.rel_type, t3.check_status, " +
                "b.name as building_name, u.name as unit_name " +
                "FROM eh_house_info t1 " +
                "INNER JOIN eh_house_owner_rel t3 ON t1.house_id = t3.house_id " +
                "INNER JOIN eh_owner t2 ON t2.owner_id = t3.owner_id " +
                "INNER JOIN eh_community t4 ON t2.oc_id = t2.community_id " +
                "LEFT JOIN eh_building b ON t1.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON t1.unit_id = u.unit_id " +
                "WHERE t2.mobile = ? " +
                "ORDER BY t3.is_default DESC, t3.create_time DESC",
                getCurrentUser().getMobile());
            if(houseRecordList!=null){
                for(Record record:houseRecordList){
                    HouseInfo house = houseInfoService.recordToObj(record);
                    list.add(house);
                }
            }
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", list);
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取房屋列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取房屋列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取房屋详情
     */
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam Long houseId) {
        try {
            Long wxUserId = getCurrentUserId();
            Record info = Db.findFirst("select * from eh_house_info where house_id = ?",houseId);
            if (info == null) {
                return AjaxResult.error("房屋不存在");
            }
            return AjaxResult.success(info.toMap());
        } catch (Exception e) {
            return AjaxResult.error("获取房屋详情失败: " + e.getMessage());
        }
    }

    /**
     * 添加房屋
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody HouseInfo houseInfo) {
        try {
            // 检查该用户的房屋数量是否超过限制
            int count = houseInfoService.countHouseByUserId(getCurrentUser().getOwnerId());
            if (count >= 5) {
                return AjaxResult.error("最多只能添加5套房屋");
            }
            // 设置初始状态为待审核
            houseInfo.setStatus(0);
            // 添加房屋
            int rows = houseInfoService.addHouse(houseInfo);
            if(rows>0){
                Record houseOwnerRel = new Record();
                houseOwnerRel.set("rel_id", Seq.getId());
                houseOwnerRel.set("house_id", houseInfo.getHouseId());
                houseOwnerRel.set("owner_id", getCurrentUser().getOwnerId());
                houseOwnerRel.set("is_default", 0);
                Db.save("eh_house_owner_rel","rel_id",houseOwnerRel);
            }
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("添加失败");
        } catch (Exception e) {
            logger.error("添加房屋失败: " + e.getMessage(), e);
            return AjaxResult.error("添加房屋失败: " + e.getMessage());
        }
    }

    /**
     * 小程序端-更新房屋
     */
    @PostMapping("/update")
    public AjaxResult update(@RequestBody HouseInfo house) {
        try {
            Long wxUserId = getCurrentUserId();
            // 验证是否是当前用户的房屋
            HouseInfo oldHouse = houseInfoService.selectHouseById(house.getHouseId());
            if (oldHouse == null) {
                return AjaxResult.error("房屋不存在");
            }
            if (!oldHouse.getWxUserId().equals(wxUserId)) {
                return AjaxResult.error("无权修改该房屋信息");
            }

            Record houseRecord = new Record();
            house.setCommunityId(oldHouse.getCommunityId());
            house.setCommunityName(oldHouse.getCommunityName());
            house.setIsDefault(oldHouse.getIsDefault());
            houseRecord.set("house_id", house.getHouseId());
           boolean result = Db.update("eh_house_info","house_id",houseRecord);
            return result ? AjaxResult.success() : AjaxResult.error("修改失败");
        } catch (Exception e) {
            return AjaxResult.error("修改房屋失败: " + e.getMessage());
        }
    }

    /**
     * 删除房屋
     */
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody Map<String, Long> params) {
        try {
            Long wxUserId = getCurrentUserId();
            Long houseId = params.get("houseId");
            // 验证是否是当前用户的房屋
            HouseInfo house = houseInfoService.selectHouseById(houseId);
            if (house == null) {
                return AjaxResult.error("房屋不存在");
            }
            if (!house.getWxUserId().equals(wxUserId)) {
                return AjaxResult.error("无权删除该房屋");
            }

            int rows = houseInfoService.deleteHouse(houseId);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("删除失败");
        } catch (Exception e) {
            return AjaxResult.error("删除房屋失败: " + e.getMessage());
        }
    }

    /**
     * 设置默认房屋
     */
    @PostMapping("/set-default")
    public AjaxResult setDefault(@RequestBody Map<String, Long> params) {
        try {
            String ownerId = getCurrentUser().getOwnerId();
            Long houseId = params.get("id");
            // 验证是否是当前用户的房屋
            Record houseRecord = Db.findFirst("select * from eh_house_info where house_id = ?",houseId);
            if (houseRecord == null) {
                return AjaxResult.error("房屋不存在");
            }
            int rows = houseInfoService.setDefaultHouse(houseId, ownerId);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("设置失败");
        } catch (Exception e) {
            return AjaxResult.error("设置默认房屋失败: " + e.getMessage());
        }
    }

    /**
     * 解除房屋绑定
     */
    @PostMapping("/unbind")
    public AjaxResult unbind(@RequestBody Map<String, String> params) {
        try {
            String ownerId = getCurrentUser().getOwnerId();
            String houseId = params.get("houseId");

            if (houseId == null) {
                return AjaxResult.error("房屋ID不能为空");
            }

            if("0".equals(getCurrentUser().getOpenId())){
                return AjaxResult.error("演示账号不能解绑房屋");
            }

            // 查找当前用户与该房屋的绑定关系
            Record rel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ?",
                houseId, ownerId
            );

            if (rel == null) {
                return AjaxResult.error("未找到绑定关系");
            }

            String relId = rel.getStr("rel_id");
            Integer isDefault = rel.getInt("is_default");

            // 检查是否为默认房屋
            if (isDefault != null && isDefault == 1) {
                // 检查用户是否还有其他房屋
                Long otherHouseCount = Db.queryLong(
                    "SELECT COUNT(*) FROM eh_house_owner_rel WHERE owner_id = ? AND house_id != ?",
                    ownerId, houseId
                );

                if (otherHouseCount > 0) {
                    // 如果有其他房屋，先将第一个其他房屋设为默认
                    Record otherHouse = Db.findFirst(
                        "SELECT * FROM eh_house_owner_rel WHERE owner_id = ? AND house_id != ? ORDER BY create_time ASC LIMIT 1",
                        ownerId, houseId
                    );
                    if (otherHouse != null) {
                        houseInfoService.setDefaultHouse(otherHouse.getLong("house_id"), ownerId);
                    }
                } else {
                    // 如果这是最后一套房屋，清除默认房屋设置
                    Db.update("UPDATE eh_owner SET house_id = '',housu_name = '' WHERE owner_id = ?", ownerId);
                }
            }

            // 删除绑定关系
            boolean success = Db.deleteById("eh_house_owner_rel", "rel_id", relId);

            if (success) {
                houseInfoService.updateHouseOwnerInfo(houseId);
                // 更新业主的房屋信息字符串
                houseInfoService.updateOwnerHouseInfo(ownerId);
                return AjaxResult.success("解绑成功");
            } else {
                return AjaxResult.error("解绑失败");
            }

        } catch (Exception e) {
            logger.error("解除房屋绑定失败: " + e.getMessage(), e);
            return AjaxResult.error("解除房屋绑定失败: " + e.getMessage());
        }
    }
}